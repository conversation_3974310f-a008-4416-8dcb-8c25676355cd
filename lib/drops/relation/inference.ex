defmodule Drops.Relation.Inference do
  alias Drops.Relation.SQL
  alias Drops.Relation.Schema.Field

  def infer_schema(relation, name, repo) do
    # Use the unified schema inference implementation
    drops_schema = SQL.Inference.infer_from_table(name, repo)

    # Get optional Ecto associations definitions AST
    association_definitions = Module.get_attribute(relation, :associations, [])

    # Generate the Ecto schema AST using the new approach
    ecto_schema_ast =
      combine_schema_with_associations_and_custom_fields(
        drops_schema,
        association_definitions,
        # No custom fields for this function
        [],
        name
      )

    {ecto_schema_ast, drops_schema}
  end

  def infer_schema_fields_only(_relation, name, repo) do
    # Use the unified schema inference implementation
    # Return only the Drops.Relation.Schema - no more Ecto AST caching
    SQL.Inference.infer_from_table(name, repo)
  end

  # Simplified function that works directly with Drops.Relation.Schema
  # and merges custom fields with cached schema fields.
  def combine_schema_with_associations_and_custom_fields(
        drops_schema,
        association_definitions,
        custom_fields,
        table_name
      ) do
    # Process custom fields into Field structs using simplified approach
    custom_field_structs = process_custom_fields(custom_fields)

    # Merge custom fields with cached schema fields using Field.merge
    # Custom fields take precedence (user-defined ecto types are respected)
    # but metadata from inferred fields is preserved when not overridden
    custom_field_map = Map.new(custom_field_structs, &{&1.name, &1})

    # Merge fields: for each inferred field, check if there's a custom field to merge with
    merged_fields =
      Enum.map(drops_schema.fields, fn inferred_field ->
        case Map.get(custom_field_map, inferred_field.name) do
          nil -> inferred_field
          custom_field -> Field.merge(inferred_field, custom_field)
        end
      end)

    # Add any custom fields that don't have corresponding inferred fields
    inferred_field_names = MapSet.new(drops_schema.fields, & &1.name)

    additional_custom_fields =
      Enum.reject(custom_field_structs, fn field ->
        MapSet.member?(inferred_field_names, field.name)
      end)

    # Combine all fields
    all_fields = merged_fields ++ additional_custom_fields

    # Get field names to exclude due to associations (foreign keys)
    association_field_names = extract_association_field_names(association_definitions)

    # Check if we have both timestamp fields in the merged fields
    has_timestamps =
      Enum.any?(all_fields, &(&1.name == :inserted_at)) and
        Enum.any?(all_fields, &(&1.name == :updated_at))

    # Filter out fields that should be excluded
    # Exclude association foreign key fields and timestamp fields if using timestamps() macro
    additional_exclusions =
      if has_timestamps do
        [:inserted_at, :updated_at, :id]
      else
        []
      end

    all_exclusions = association_field_names ++ additional_exclusions

    final_fields =
      Enum.reject(all_fields, fn field ->
        field.name in all_exclusions
      end)

    # Generate field definitions from the final fields
    field_definitions =
      Enum.map(final_fields, fn field ->
        # Handle parameterized types by extracting options
        {type, type_opts} = extract_type_and_options(field.ecto_type)

        base_opts = if field.source != field.name, do: [source: field.source], else: []
        all_opts = Keyword.merge(type_opts, base_opts)

        if all_opts == [] do
          quote do
            Ecto.Schema.field(unquote(field.name), unquote(type))
          end
        else
          quote do
            Ecto.Schema.field(unquote(field.name), unquote(type), unquote(all_opts))
          end
        end
      end)

    # Add timestamps() macro if we have both timestamp fields in the merged fields
    all_field_definitions =
      if has_timestamps do
        field_definitions ++
          [
            quote do
              timestamps()
            end
          ]
      else
        field_definitions
      end

    # Create the final schema AST
    quote location: :keep do
      schema unquote(table_name) do
        (unquote_splicing(all_field_definitions))

        unquote(association_definitions)
      end
    end
  end

  # Simplified custom field processing using manual expansion of accumulated fields
  defp process_custom_fields(custom_fields) do
    # Expand accumulated fields into regular 3-element tuples by manually handling AST patterns
    expanded_fields =
      Enum.map(custom_fields, fn field_tuple ->
        case field_tuple do
          {name, type, opts} when is_atom(name) and is_list(opts) ->
            # Already a proper tuple with expanded type
            {name, expand_type_ast(type), opts}

          {name, type, opts} when is_atom(name) ->
            # Type might be AST, expand it
            {name, expand_type_ast(type), opts}

          {name, type} when is_atom(name) ->
            # Missing opts, add empty list
            {name, expand_type_ast(type), []}

          _ ->
            # Unexpected format, try to handle gracefully
            field_tuple
        end
      end)

    # Process the expanded tuples into Field structs
    Enum.map(expanded_fields, fn {name, type, opts} ->
      # Extract source from opts if provided, otherwise use field name
      source = Keyword.get(opts, :source, name)

      # Extract meta information from opts
      meta = %{
        is_nullable: Keyword.get(opts, :null),
        default: Keyword.get(opts, :default),
        check_constraints: []
      }

      # Build ecto_type directly from type and opts (now expanded)
      ecto_type = build_ecto_type_simplified(type, opts)

      # Use simple type normalization
      normalized_type = normalize_type_simplified(ecto_type)

      Field.new(name, normalized_type, ecto_type, source, meta)
    end)
  end

  # Manually expand common AST patterns to actual modules/atoms
  defp expand_type_ast({:__aliases__, _, [:Ecto, :Enum]}), do: Ecto.Enum
  defp expand_type_ast({:__aliases__, _, [:Ecto, :UUID]}), do: Ecto.UUID
  defp expand_type_ast(type) when is_atom(type), do: type
  # For other complex types, return as-is
  defp expand_type_ast(type), do: type

  # Simplified ecto_type building (now works with expanded types)
  defp build_ecto_type_simplified(type, opts) do
    case type do
      Ecto.Enum ->
        case Keyword.get(opts, :values) do
          nil -> type
          values -> {Ecto.Enum, values: values}
        end

      _ ->
        type
    end
  end

  # Simplified type normalization
  defp normalize_type_simplified(type) do
    case type do
      :string ->
        :string

      :integer ->
        :integer

      :boolean ->
        :boolean

      :float ->
        :float

      :decimal ->
        :decimal

      :date ->
        :date

      :time ->
        :time

      :naive_datetime ->
        :naive_datetime

      :utc_datetime ->
        :utc_datetime

      :binary ->
        :binary

      :id ->
        :integer

      :binary_id ->
        :binary

      # Default for parameterized types
      {module, _opts} when is_atom(module) ->
        :string

      {container, inner_type} when is_atom(container) ->
        {container, normalize_type_simplified(inner_type)}

      # Default fallback
      _ ->
        :string
    end
  end

  # Helper function to extract type and options from ecto_type for field generation
  defp extract_type_and_options(ecto_type) do
    case ecto_type do
      {type, opts} when is_list(opts) ->
        {type, opts}

      {type, opts} when is_map(opts) ->
        {type, Map.to_list(opts)}

      type ->
        {type, []}
    end
  end

  # Helper function to extract foreign key field names from association definitions
  # that should be excluded from inferred schema. For belongs_to associations,
  # Ecto automatically creates the foreign key field UNLESS define_field: false
  # is specified, in which case the user is responsible for defining the field.
  defp extract_association_field_names(association_definitions) do
    case association_definitions do
      # Handle single belongs_to association with options
      {:belongs_to, _meta, [field_name, _related_module, opts]} when is_list(opts) ->
        # Only exclude the foreign key field if define_field is NOT false
        # (i.e., when Ecto will create it automatically)
        if Keyword.get(opts, :define_field, true) == false do
          # User specified define_field: false, so they'll define the field themselves
          # Don't exclude it from inferred schema
          []
        else
          # Ecto will create the field automatically, so exclude it from inferred schema
          [infer_foreign_key_field_name(field_name, opts)]
        end

      {:belongs_to, _meta, [field_name, _related_module]} ->
        # No define_field option specified, defaults to true, so Ecto will create the field
        [infer_foreign_key_field_name(field_name, [])]

      # Handle block with multiple associations
      {:__block__, _meta, associations} when is_list(associations) ->
        Enum.flat_map(associations, &extract_association_field_names/1)

      # Handle other association types (has_many, many_to_many, etc.)
      {assoc_type, _meta, _args} when assoc_type in [:has_many, :has_one, :many_to_many] ->
        # These don't create foreign key fields in the current table
        []

      # Handle any other case
      _ ->
        []
    end
  end

  # Helper function to infer the foreign key field name from association name and options
  defp infer_foreign_key_field_name(association_name, opts) do
    case Keyword.get(opts, :foreign_key) do
      nil ->
        # Default foreign key naming: association_name + "_id"
        String.to_atom("#{association_name}_id")

      foreign_key when is_atom(foreign_key) ->
        foreign_key
    end
  end
end
