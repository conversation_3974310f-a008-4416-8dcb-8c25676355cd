defmodule Drops.Relation.SQL.IntrospectionMetadataTest do
  use Drops.RelationCase, async: true

  alias Drops.Relation.SQL.Introspector

  describe "enhanced column introspection" do
    @describetag adapter: :sqlite

    test "SQLite introspection includes metadata" do
      columns = Introspector.introspect_table_columns(Drops.Repos.Sqlite, "metadata_test")

      assert is_list(columns)
      assert length(columns) > 0

      # Find specific columns to test metadata
      status_column = Enum.find(columns, &(&1.name == "status"))
      description_column = Enum.find(columns, &(&1.name == "description"))
      score_column = Enum.find(columns, &(&1.name == "score"))

      # Test status column (has default, not null)
      assert status_column
      assert status_column.default == "active"
      assert status_column.is_nullable == false
      assert status_column.not_null == true

      # Test description column (nullable)
      assert description_column
      assert description_column.is_nullable == true
      assert description_column.not_null == false

      # Test score column (has check constraint)
      assert score_column
      assert score_column.is_nullable == false
      assert is_list(score_column.check_constraints)
      # Should have the check constraint we defined
      assert Enum.any?(score_column.check_constraints, fn constraint ->
               String.contains?(constraint, "score >= 0") and
                 String.contains?(constraint, "score <= 100")
             end)
    end

    test "Postgres introspection includes metadata" do
      columns =
        Introspector.introspect_table_columns(Drops.Repos.Postgres, "metadata_test")

      assert is_list(columns)
      assert length(columns) > 0

      # Find specific columns to test metadata
      status_column = Enum.find(columns, &(&1.name == "status"))
      description_column = Enum.find(columns, &(&1.name == "description"))
      score_column = Enum.find(columns, &(&1.name == "score"))

      # Test status column (has default, not null)
      assert status_column
      assert status_column.default == "active"
      assert status_column.is_nullable == false
      assert status_column.not_null == true

      # Test description column (nullable)
      assert description_column
      assert description_column.is_nullable == true
      assert description_column.not_null == false

      # Test score column (has check constraints)
      assert score_column
      assert score_column.is_nullable == false
      assert is_list(score_column.check_constraints)
      # Should have both check constraints we defined
      assert length(score_column.check_constraints) >= 1
    end
  end

  describe "default value parsing" do
    @describetag adapter: :sqlite

    test "SQLite parses various default values correctly" do
      # Test with a table that has various default types
      columns = Introspector.introspect_table_columns(Drops.Repos.Sqlite, "metadata_test")

      status_column = Enum.find(columns, &(&1.name == "status"))
      priority_column = Enum.find(columns, &(&1.name == "priority"))
      is_enabled_column = Enum.find(columns, &(&1.name == "is_enabled"))

      assert status_column.default == "active"
      assert priority_column.default == 1
      assert is_enabled_column.default == true
    end

    test "Postgres parses various default values correctly" do
      columns =
        Introspector.introspect_table_columns(Drops.Repos.Postgres, "metadata_test")

      status_column = Enum.find(columns, &(&1.name == "status"))
      priority_column = Enum.find(columns, &(&1.name == "priority"))
      is_enabled_column = Enum.find(columns, &(&1.name == "is_enabled"))

      assert status_column.default == "active"
      assert priority_column.default == 1
      assert is_enabled_column.default == true
    end
  end
end
